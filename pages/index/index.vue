<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="hospital-info">
					<image class="hospital-logo" src="/static/hospital-logo.png" mode="aspectFit"></image>
					<view class="hospital-text">
						<text class="hospital-name">西虹市第一人民医院</text>
						<text class="hospital-slogan">智好 互联网医院2.0</text>
					</view>
				</view>
				<view class="navbar-actions">
					<text class="scan-icon">⋯</text>
					<text class="location-icon">📍</text>
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 用户信息卡片 -->
			<view class="user-card">
				<view class="user-info">
					<image class="avatar" src="/static/default-avatar.png" mode="aspectFit"></image>
					<view class="user-details">
						<text class="username">刘浩存</text>
						<text class="user-id">身份证号：370***********24</text>
						<text class="balance">门诊余额：¥ 128.56</text>
					</view>
				</view>
				<view class="qr-code">
					<text class="qr-icon">⚏</text>
					<text class="qr-text">点击出示</text>
				</view>
			</view>

			<!-- 公告栏 -->
			<view class="announcement">
				<text class="announcement-icon">📢</text>
				<text class="announcement-text">公告 全新前端小程序正式上线启用</text>
				<text class="announcement-arrow">></text>
			</view>

			<!-- 核心功能区 - AI智能问诊 -->
			<view class="ai-consultation-section">
				<view class="ai-card" @click="goToAIConsultation">
					<view class="ai-content">
						<view class="ai-icon">🤖</view>
						<view class="ai-text">
							<text class="ai-title">AI智能问诊</text>
							<text class="ai-subtitle">告诉我哪里不舒服，我来帮你找对科室</text>
						</view>
					</view>
					<view class="ai-doctor">
						<image class="doctor-avatar" src="/static/ai-doctor.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>

			<!-- 快捷功能区 -->
			<view class="quick-actions">
				<view class="action-card green" @click="goToTraditionalAppointment">
					<text class="action-icon">📋</text>
					<text class="action-title">挂号/预约</text>
					<text class="action-subtitle">门诊挂号/排队查询</text>
				</view>
				<view class="action-card blue" @click="goToRecharge">
					<text class="action-icon">💳</text>
					<text class="action-title">门诊充值</text>
					<text class="action-subtitle">就诊卡快速充值</text>
				</view>
			</view>

			<!-- 相关服务网格 -->
			<view class="services-section">
				<text class="section-title">相关服务</text>
				<view class="services-grid">
					<view class="service-item" @click="goToMyAppointments">
						<text class="service-icon">📅</text>
						<text class="service-name">我的预约</text>
					</view>
					<view class="service-item">
						<text class="service-icon">💊</text>
						<text class="service-name">门诊费用</text>
					</view>
					<view class="service-item">
						<text class="service-icon">📋</text>
						<text class="service-name">门诊报告</text>
					</view>
					<view class="service-item">
						<text class="service-icon">🏥</text>
						<text class="service-name">影像报告</text>
					</view>
					<view class="service-item">
						<text class="service-icon">📊</text>
						<text class="service-name">电子票据</text>
					</view>
					<view class="service-item">
						<text class="service-icon">📝</text>
						<text class="service-name">门诊病历</text>
					</view>
					<view class="service-item">
						<text class="service-icon">💬</text>
						<text class="service-name">门诊结算</text>
					</view>
					<view class="service-item">
						<text class="service-icon">📱</text>
						<text class="service-name">线上退款</text>
					</view>
				</view>
			</view>

			<!-- 健康讲堂 -->
			<view class="health-section">
				<text class="section-title">健康讲堂</text>
				<!-- 健康内容可以后续添加 -->
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
	},
	methods: {
		// 跳转到AI智能问诊
		goToAIConsultation() {
			uni.navigateTo({
				url: '/pages/ai-consultation/ai-consultation'
			});
		},
		// 跳转到传统预约挂号
		goToTraditionalAppointment() {
			uni.navigateTo({
				url: '/pages/appointment/department-list'
			});
		},
		// 跳转到我的预约
		goToMyAppointments() {
			uni.switchTab({
				url: '/pages/my-appointments/my-appointments'
			});
		},
		// 跳转到充值页面
		goToRecharge() {
			uni.showToast({
				title: '充值功能开发中',
				icon: 'none'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #2E8B57 0%, #20B2AA 100%);
	min-height: 100vh;
}

/* 自定义导航栏 */
.custom-navbar {
	background: transparent;
	padding: 0 30rpx 20rpx;
}

.navbar-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
}

.hospital-info {
	display: flex;
	align-items: center;
}

.hospital-logo {
	width: 60rpx;
	height: 60rpx;
	margin-right: 20rpx;
}

.hospital-text {
	display: flex;
	flex-direction: column;
}

.hospital-name {
	color: white;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 5rpx;
}

.hospital-slogan {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
}

.navbar-actions {
	display: flex;
	align-items: center;
}

.scan-icon,
.location-icon {
	color: white;
	font-size: 40rpx;
	margin-left: 30rpx;
}

/* 主要内容区域 */
.main-content {
	background: #f5f5f5;
	border-radius: 40rpx 40rpx 0 0;
	padding: 30rpx;
	min-height: calc(100vh - 200rpx);
}

/* 用户信息卡片 */
.user-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.user-info {
	display: flex;
	align-items: center;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
}

.user-details {
	display: flex;
	flex-direction: column;
}

.username {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.user-id {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 8rpx;
}

.balance {
	font-size: 28rpx;
	color: #2E8B57;
	font-weight: bold;
}

.qr-code {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qr-icon {
	font-size: 60rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.qr-text {
	font-size: 24rpx;
	color: #666;
}

/* 公告栏 */
.announcement {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 15rpx;
	padding: 20rpx 30rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
}

.announcement-icon {
	font-size: 30rpx;
	margin-right: 15rpx;
}

.announcement-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.announcement-arrow {
	font-size: 30rpx;
	color: #999;
}

/* AI智能问诊核心区域 */
.ai-consultation-section {
	margin-bottom: 30rpx;
}

.ai-card {
	background: linear-gradient(135deg, #4CAF50 0%, #2E8B57 100%);
	border-radius: 25rpx;
	padding: 40rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 8rpx 30rpx rgba(46, 139, 87, 0.3);
	position: relative;
	overflow: hidden;
}

.ai-card::before {
	content: '';
	position: absolute;
	top: -50%;
	right: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
	animation: shimmer 3s infinite;
}

@keyframes shimmer {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.ai-content {
	display: flex;
	align-items: center;
	z-index: 2;
}

.ai-icon {
	font-size: 60rpx;
	margin-right: 25rpx;
}

.ai-text {
	display: flex;
	flex-direction: column;
}

.ai-title {
	color: white;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.ai-subtitle {
	color: rgba(255, 255, 255, 0.9);
	font-size: 26rpx;
	line-height: 1.4;
}

.ai-doctor {
	z-index: 2;
}

.doctor-avatar {
	width: 120rpx;
	height: 120rpx;
}

/* 快捷功能区 */
.quick-actions {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.action-card {
	flex: 1;
	border-radius: 20rpx;
	padding: 30rpx 25rpx;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	position: relative;
	overflow: hidden;
}

.action-card.green {
	background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.action-card.blue {
	background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.action-icon {
	font-size: 50rpx;
	margin-bottom: 15rpx;
}

.action-title {
	color: white;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.action-subtitle {
	color: rgba(255, 255, 255, 0.9);
	font-size: 24rpx;
	line-height: 1.3;
}

/* 相关服务网格 */
.services-section {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 25rpx;
	display: block;
}

.services-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 25rpx;
}

.service-item {
	background: white;
	border-radius: 15rpx;
	padding: 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}

.service-item:active {
	transform: scale(0.95);
	box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);
}

.service-icon {
	font-size: 45rpx;
	margin-bottom: 15rpx;
}

.service-name {
	font-size: 24rpx;
	color: #333;
	text-align: center;
	line-height: 1.2;
}

/* 健康讲堂 */
.health-section {
	margin-bottom: 50rpx;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.services-grid {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media screen and (max-width: 600rpx) {
	.quick-actions {
		flex-direction: column;
	}

	.action-card {
		margin-bottom: 15rpx;
	}

	.services-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}
</style>
