<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-container">
				<text class="search-icon">🔍</text>
				<input class="search-input" v-model="searchKeyword" placeholder="搜索科室" @input="filterDepartments" />
			</view>
		</view>
		
		<!-- 常用科室 -->
		<view class="common-departments" v-if="!searchKeyword">
			<view class="section-title">
				<text>常用科室</text>
			</view>
			<view class="department-grid">
				<view class="department-item common" v-for="dept in commonDepartments" :key="dept.id" 
					  @click="selectDepartment(dept)">
					<view class="department-icon">{{ dept.icon }}</view>
					<text class="department-name">{{ dept.name }}</text>
					<text class="department-desc">{{ dept.description }}</text>
				</view>
			</view>
		</view>
		
		<!-- 全部科室 -->
		<view class="all-departments">
			<view class="section-title">
				<text>{{ searchKeyword ? '搜索结果' : '全部科室' }}</text>
			</view>
			<view class="department-list">
				<view class="department-item" v-for="dept in filteredDepartments" :key="dept.id" 
					  @click="selectDepartment(dept)">
					<view class="department-info">
						<view class="department-icon">{{ dept.icon }}</view>
						<view class="department-details">
							<text class="department-name">{{ dept.name }}</text>
							<text class="department-desc">{{ dept.description }}</text>
							<text class="doctor-count">{{ dept.doctorCount }}位医生</text>
						</view>
					</view>
					<text class="arrow-icon">></text>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="searchKeyword && filteredDepartments.length === 0">
			<text class="empty-icon">🔍</text>
			<text class="empty-text">未找到相关科室</text>
			<text class="empty-desc">请尝试其他关键词</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			commonDepartments: [
				{
					id: 1,
					name: '内科',
					description: '发热、咳嗽、胸闷等',
					icon: '🩺',
					doctorCount: 12
				},
				{
					id: 2,
					name: '外科',
					description: '外伤、手术等',
					icon: '🔪',
					doctorCount: 8
				},
				{
					id: 3,
					name: '儿科',
					description: '儿童疾病',
					icon: '👶',
					doctorCount: 6
				},
				{
					id: 4,
					name: '妇科',
					description: '妇科疾病',
					icon: '👩',
					doctorCount: 5
				}
			],
			allDepartments: [
				{
					id: 1,
					name: '内科',
					description: '发热、咳嗽、胸闷、高血压、糖尿病等',
					icon: '🩺',
					doctorCount: 12
				},
				{
					id: 2,
					name: '外科',
					description: '外伤、骨折、手术、肿瘤等',
					icon: '🔪',
					doctorCount: 8
				},
				{
					id: 3,
					name: '儿科',
					description: '儿童发热、咳嗽、腹泻等',
					icon: '👶',
					doctorCount: 6
				},
				{
					id: 4,
					name: '妇科',
					description: '妇科炎症、月经不调等',
					icon: '👩',
					doctorCount: 5
				},
				{
					id: 5,
					name: '眼科',
					description: '视力下降、眼部疾病等',
					icon: '👁️',
					doctorCount: 4
				},
				{
					id: 6,
					name: '耳鼻喉科',
					description: '鼻炎、咽炎、听力问题等',
					icon: '👂',
					doctorCount: 3
				},
				{
					id: 7,
					name: '皮肤科',
					description: '皮疹、过敏、皮肤病等',
					icon: '🧴',
					doctorCount: 4
				},
				{
					id: 8,
					name: '骨科',
					description: '骨折、关节疼痛等',
					icon: '🦴',
					doctorCount: 6
				},
				{
					id: 9,
					name: '心内科',
					description: '心脏病、胸痛等',
					icon: '❤️',
					doctorCount: 5
				},
				{
					id: 10,
					name: '神经内科',
					description: '头痛、失眠、神经系统疾病',
					icon: '🧠',
					doctorCount: 4
				}
			],
			filteredDepartments: []
		}
	},
	onLoad() {
		this.filteredDepartments = this.allDepartments;
	},
	methods: {
		// 筛选科室
		filterDepartments() {
			if (!this.searchKeyword.trim()) {
				this.filteredDepartments = this.allDepartments;
				return;
			}
			
			this.filteredDepartments = this.allDepartments.filter(dept => 
				dept.name.includes(this.searchKeyword) || 
				dept.description.includes(this.searchKeyword)
			);
		},
		
		// 选择科室
		selectDepartment(department) {
			uni.navigateTo({
				url: `/pages/appointment/doctor-list?departmentId=${department.id}&departmentName=${encodeURIComponent(department.name)}`
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
	background: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.search-input-container {
	display: flex;
	align-items: center;
	background: #f8f8f8;
	border-radius: 50rpx;
	padding: 0 30rpx;
}

.search-icon {
	font-size: 30rpx;
	color: #999;
	margin-right: 20rpx;
}

.search-input {
	flex: 1;
	border: none;
	background: transparent;
	font-size: 30rpx;
	padding: 25rpx 0;
}

/* 区块标题 */
.section-title {
	padding: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 常用科室网格 */
.department-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
	padding: 0 30rpx 30rpx;
}

.department-item.common {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.department-item.common:active {
	transform: scale(0.95);
}

.department-item.common .department-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
}

.department-item.common .department-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.department-item.common .department-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.3;
}

/* 全部科室列表 */
.department-list {
	background: white;
	margin: 0 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.department-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background 0.3s ease;
}

.department-item:last-child {
	border-bottom: none;
}

.department-item:active {
	background: #f8f8f8;
}

.department-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.department-item .department-icon {
	font-size: 50rpx;
	margin-right: 25rpx;
}

.department-details {
	display: flex;
	flex-direction: column;
}

.department-item .department-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.department-item .department-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 5rpx;
	line-height: 1.3;
}

.doctor-count {
	font-size: 24rpx;
	color: #2E8B57;
}

.arrow-icon {
	font-size: 30rpx;
	color: #ccc;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 30rpx;
}

.empty-icon {
	font-size: 100rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #999;
}
</style>
