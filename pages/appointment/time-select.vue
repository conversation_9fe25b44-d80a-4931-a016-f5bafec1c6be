<template>
	<view class="container">
		<!-- 医生信息卡片 -->
		<view class="doctor-card" v-if="selectedDoctor">
			<view class="doctor-info">
				<image class="doctor-avatar" :src="selectedDoctor.avatar" mode="aspectFit"></image>
				<view class="doctor-details">
					<text class="doctor-name">{{ selectedDoctor.name }}</text>
					<text class="doctor-title">{{ selectedDoctor.title }}</text>
					<text class="doctor-department">{{ selectedDoctor.department }}</text>
					<text class="consultation-fee">挂号费：¥{{ selectedDoctor.consultationFee }}</text>
				</view>
			</view>
		</view>

		<!-- 日期选择 -->
		<view class="date-section">
			<view class="section-title">
				<text>选择日期</text>
			</view>
			<scroll-view class="date-scroll" scroll-x="true" show-scrollbar="false">
				<view class="date-list">
					<view class="date-item" v-for="(date, index) in availableDates" :key="index"
						:class="{ active: selectedDateIndex === index }" @click="selectDate(index)">
						<text class="date-day">{{ date.day }}</text>
						<text class="date-date">{{ date.date }}</text>
						<text class="date-status">{{ date.status }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 时间段选择 -->
		<view class="time-section">
			<view class="section-title">
				<text>选择时间段</text>
			</view>
			<view class="time-periods">
				<view class="period-group" v-for="period in timeSlots" :key="period.period">
					<view class="period-header">
						<text class="period-name">{{ period.period }}</text>
						<text class="period-time">{{ period.timeRange }}</text>
					</view>
					<view class="time-slots">
						<view class="time-slot" v-for="slot in period.slots" :key="slot.id" :class="{
							active: selectedTimeSlot && selectedTimeSlot.id === slot.id,
							disabled: !slot.available
						}" @click="selectTimeSlot(slot)">
							<text class="slot-time">{{ slot.time }}</text>
							<text class="slot-status">{{ slot.available ? '可约' : '已满' }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 预约信息确认 -->
		<view class="appointment-summary" v-if="selectedTimeSlot">
			<view class="summary-title">
				<text>预约信息确认</text>
			</view>
			<view class="summary-content">
				<view class="summary-item">
					<text class="item-label">🏥 科室：</text>
					<text class="item-value">{{ selectedDoctor.department }}</text>
				</view>
				<view class="summary-item">
					<text class="item-label">👨‍⚕️ 医生：</text>
					<text class="item-value">{{ selectedDoctor.name }}</text>
				</view>
				<view class="summary-item">
					<text class="item-label">📅 时间：</text>
					<text class="item-value">{{ getSelectedDateTime() }}</text>
				</view>
				<view class="summary-item">
					<text class="item-label">💰 费用：</text>
					<text class="item-value">{{ selectedDoctor.consultationFee }}元</text>
				</view>
				<view class="summary-item">
					<text class="item-label">📍 地点：</text>
					<text class="item-value">门诊2楼{{ selectedDoctor.department }}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="btn-secondary" @click="goBack">返回修改</button>
			<button class="btn-primary" :disabled="!selectedTimeSlot" @click="confirmAppointment">
				确认预约
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			selectedDoctor: null,
			selectedDateIndex: 0,
			selectedTimeSlot: null,
			availableDates: [],
			timeSlots: []
		}
	},
	onLoad(options) {
		if (options.doctor) {
			this.selectedDoctor = JSON.parse(decodeURIComponent(options.doctor));
		}
		this.initializeDates();
		this.initializeTimeSlots();
	},
	methods: {
		// 初始化可选日期
		initializeDates() {
			const dates = [];
			const today = new Date();

			for (let i = 0; i < 7; i++) {
				const date = new Date(today);
				date.setDate(today.getDate() + i);

				const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
				const day = i === 0 ? '今天' : i === 1 ? '明天' : dayNames[date.getDay()];
				const dateStr = `${date.getMonth() + 1}/${date.getDate()}`;

				// 模拟号源状态
				let status = '有号';
				if (i === 0 && date.getHours() > 16) status = '停诊';
				if (i === 3 || i === 5) status = '约满';

				dates.push({
					day,
					date: dateStr,
					status,
					fullDate: date
				});
			}

			this.availableDates = dates;
		},

		// 初始化时间段
		initializeTimeSlots() {
			this.timeSlots = [
				{
					period: '上午',
					timeRange: '08:00-12:00',
					slots: [
						{ id: 1, time: '08:00-08:30', available: true },
						{ id: 2, time: '08:30-09:00', available: true },
						{ id: 3, time: '09:00-09:30', available: false },
						{ id: 4, time: '09:30-10:00', available: true },
						{ id: 5, time: '10:00-10:30', available: true },
						{ id: 6, time: '10:30-11:00', available: false },
						{ id: 7, time: '11:00-11:30', available: true },
						{ id: 8, time: '11:30-12:00', available: true }
					]
				},
				{
					period: '下午',
					timeRange: '14:00-18:00',
					slots: [
						{ id: 9, time: '14:00-14:30', available: true },
						{ id: 10, time: '14:30-15:00', available: false },
						{ id: 11, time: '15:00-15:30', available: true },
						{ id: 12, time: '15:30-16:00', available: true },
						{ id: 13, time: '16:00-16:30', available: false },
						{ id: 14, time: '16:30-17:00', available: true },
						{ id: 15, time: '17:00-17:30', available: true },
						{ id: 16, time: '17:30-18:00', available: false }
					]
				}
			];
		},

		// 选择日期
		selectDate(index) {
			if (this.availableDates[index].status === '停诊' || this.availableDates[index].status === '约满') {
				uni.showToast({
					title: this.availableDates[index].status,
					icon: 'none'
				});
				return;
			}
			this.selectedDateIndex = index;
			this.selectedTimeSlot = null; // 重置时间选择
		},

		// 选择时间段
		selectTimeSlot(slot) {
			if (!slot.available) {
				uni.showToast({
					title: '该时间段已满',
					icon: 'none'
				});
				return;
			}
			this.selectedTimeSlot = slot;
		},

		// 获取选中的日期时间
		getSelectedDateTime() {
			if (!this.selectedTimeSlot) return '';
			const date = this.availableDates[this.selectedDateIndex];
			return `${date.day} ${date.date} ${this.selectedTimeSlot.time}`;
		},

		// 确认预约
		confirmAppointment() {
			if (!this.selectedTimeSlot) {
				uni.showToast({
					title: '请选择预约时间',
					icon: 'none'
				});
				return;
			}

			const appointmentData = {
				doctor: this.selectedDoctor,
				date: this.availableDates[this.selectedDateIndex],
				timeSlot: this.selectedTimeSlot,
				fee: this.selectedDoctor.consultationFee
			};

			// 模拟预约成功
			uni.showModal({
				title: '预约成功',
				content: `预约信息：\n医生：${this.selectedDoctor.name}\n时间：${this.getSelectedDateTime()}\n费用：${this.selectedDoctor.consultationFee}元\n\n温馨提醒：\n✅ 带身份证和医保卡\n✅ 提前15分钟到达\n✅ 如需空腹检查会提前通知`,
				showCancel: false,
				success: () => {
					// 跳转到我的预约页面
					uni.switchTab({
						url: '/pages/my-appointments/my-appointments'
					});
				}
			});
		},

		// 返回修改
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style scoped>
.container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 医生信息卡片 */
.doctor-card {
	background: white;
	margin: 30rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.doctor-info {
	display: flex;
	align-items: center;
}

.doctor-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 25rpx;
}

.doctor-details {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.doctor-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.doctor-title {
	font-size: 26rpx;
	color: #2E8B57;
	margin-bottom: 8rpx;
}

.doctor-department {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.consultation-fee {
	font-size: 28rpx;
	color: #2E8B57;
	font-weight: bold;
}

/* 区块标题 */
.section-title {
	padding: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 日期选择 */
.date-section {
	background: white;
	margin: 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.date-scroll {
	padding: 0 30rpx 30rpx;
}

.date-list {
	display: flex;
	gap: 20rpx;
}

.date-item {
	min-width: 120rpx;
	padding: 25rpx 20rpx;
	border-radius: 15rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #f8f8f8;
	transition: all 0.3s ease;
}

.date-item.active {
	background: #2E8B57;
	color: white;
}

.date-day {
	font-size: 26rpx;
	margin-bottom: 8rpx;
}

.date-date {
	font-size: 24rpx;
	margin-bottom: 8rpx;
}

.date-status {
	font-size: 22rpx;
	opacity: 0.8;
}

/* 时间段选择 */
.time-section {
	background: white;
	margin: 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.time-periods {
	padding: 0 30rpx 30rpx;
}

.period-group {
	margin-bottom: 40rpx;
}

.period-group:last-child {
	margin-bottom: 0;
}

.period-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding-bottom: 15rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.period-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.period-time {
	font-size: 26rpx;
	color: #666;
}

.time-slots {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15rpx;
}

.time-slot {
	padding: 20rpx;
	border-radius: 15rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #f8f8f8;
	transition: all 0.3s ease;
}

.time-slot.active {
	background: #2E8B57;
	color: white;
}

.time-slot.disabled {
	background: #f0f0f0;
	opacity: 0.5;
}

.slot-time {
	font-size: 28rpx;
	margin-bottom: 5rpx;
}

.slot-status {
	font-size: 22rpx;
	opacity: 0.8;
}

/* 预约信息确认 */
.appointment-summary {
	background: white;
	margin: 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.summary-title {
	background: linear-gradient(135deg, #2E8B57 0%, #20B2AA 100%);
	padding: 25rpx 30rpx;
	color: white;
	font-size: 32rpx;
	font-weight: bold;
}

.summary-content {
	padding: 30rpx;
}

.summary-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.summary-item:last-child {
	margin-bottom: 0;
}

.item-label {
	font-size: 28rpx;
	color: #666;
	min-width: 140rpx;
}

.item-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 底部操作栏 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.btn-secondary,
.btn-primary {
	flex: 1;
	padding: 25rpx;
	border-radius: 15rpx;
	font-size: 32rpx;
	text-align: center;
	border: none;
}

.btn-secondary {
	background: #f0f0f0;
	color: #666;
}

.btn-primary {
	background: #2E8B57;
	color: white;
}

.btn-primary[disabled] {
	background: #ccc;
	color: #999;
}
</style>
