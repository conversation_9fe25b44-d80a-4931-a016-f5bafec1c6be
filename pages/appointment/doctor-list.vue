<template>
	<view class="container">
		<!-- 科室信息 -->
		<view class="department-info">
			<text class="department-name">{{ departmentName }}</text>
			<text class="doctor-count">共{{ doctors.length }}位医生</text>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item" :class="{ active: selectedFilter === 'all' }" @click="filterDoctors('all')">
				<text>全部</text>
			</view>
			<view class="filter-item" :class="{ active: selectedFilter === 'available' }" @click="filterDoctors('available')">
				<text>有号</text>
			</view>
			<view class="filter-item" :class="{ active: selectedFilter === 'expert' }" @click="filterDoctors('expert')">
				<text>专家</text>
			</view>
			<view class="filter-item" :class="{ active: selectedFilter === 'rating' }" @click="filterDoctors('rating')">
				<text>评分高</text>
			</view>
		</view>
		
		<!-- 医生列表 -->
		<view class="doctor-list">
			<view class="doctor-card" v-for="doctor in filteredDoctors" :key="doctor.id" @click="selectDoctor(doctor)">
				<view class="doctor-header">
					<view class="doctor-basic">
						<image class="doctor-avatar" :src="doctor.avatar" mode="aspectFit"></image>
						<view class="doctor-info">
							<view class="doctor-name-row">
								<text class="doctor-name">{{ doctor.name }}</text>
								<text class="doctor-title">{{ doctor.title }}</text>
							</view>
							<text class="doctor-department">{{ doctor.department }}</text>
							<view class="doctor-rating">
								<text class="rating-stars">⭐⭐⭐⭐⭐</text>
								<text class="rating-score">{{ doctor.rating }}</text>
								<text class="rating-count">({{ doctor.reviewCount }}评价)</text>
							</view>
						</view>
					</view>
					<view class="doctor-status">
						<text class="status-text" :class="doctor.available ? 'available' : 'unavailable'">
							{{ doctor.available ? '有号' : '约满' }}
						</text>
						<text class="consultation-fee">¥{{ doctor.consultationFee }}</text>
					</view>
				</view>
				
				<view class="doctor-specialty">
					<text class="specialty-label">擅长：</text>
					<text class="specialty-text">{{ doctor.specialty }}</text>
				</view>
				
				<view class="doctor-schedule" v-if="doctor.available">
					<text class="schedule-label">可预约时间：</text>
					<view class="time-slots">
						<text class="time-slot" v-for="slot in doctor.availableSlots" :key="slot">
							{{ slot }}
						</text>
					</view>
				</view>
				
				<view class="doctor-actions">
					<button class="btn-secondary" @click.stop="viewDoctorDetail(doctor)">医生详情</button>
					<button class="btn-primary" :disabled="!doctor.available" @click.stop="bookAppointment(doctor)">
						{{ doctor.available ? '立即预约' : '暂无号源' }}
					</button>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredDoctors.length === 0">
			<text class="empty-icon">👨‍⚕️</text>
			<text class="empty-text">暂无符合条件的医生</text>
			<text class="empty-desc">请尝试调整筛选条件</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			departmentId: '',
			departmentName: '',
			selectedFilter: 'all',
			doctors: [
				{
					id: 1,
					name: '张主任',
					title: '主任医师',
					department: '内科',
					rating: '4.8',
					reviewCount: 156,
					specialty: '呼吸道感染、发热诊治、高血压、糖尿病的综合管理',
					consultationFee: 35,
					available: true,
					availableSlots: ['明天上午', '后天上午'],
					avatar: '/static/doctor-zhang.png',
					isExpert: true
				},
				{
					id: 2,
					name: '李医生',
					title: '副主任医师',
					department: '内科',
					rating: '4.6',
					reviewCount: 89,
					specialty: '消化系统疾病、胃炎、胃溃疡的诊治',
					consultationFee: 25,
					available: true,
					availableSlots: ['明天下午', '后天全天'],
					avatar: '/static/doctor-li.png',
					isExpert: false
				},
				{
					id: 3,
					name: '王教授',
					title: '主任医师',
					department: '内科',
					rating: '4.9',
					reviewCount: 203,
					specialty: '心血管疾病、冠心病、心律失常的专业治疗',
					consultationFee: 50,
					available: false,
					availableSlots: [],
					avatar: '/static/doctor-wang.png',
					isExpert: true
				},
				{
					id: 4,
					name: '陈医生',
					title: '主治医师',
					department: '内科',
					rating: '4.5',
					reviewCount: 67,
					specialty: '内分泌疾病、糖尿病、甲状腺疾病的诊治',
					consultationFee: 20,
					available: true,
					availableSlots: ['今天下午', '明天上午'],
					avatar: '/static/doctor-chen.png',
					isExpert: false
				}
			],
			filteredDoctors: []
		}
	},
	onLoad(options) {
		this.departmentId = options.departmentId || '';
		this.departmentName = decodeURIComponent(options.departmentName || '');
		this.filteredDoctors = this.doctors;
	},
	methods: {
		// 筛选医生
		filterDoctors(filter) {
			this.selectedFilter = filter;
			
			switch (filter) {
				case 'all':
					this.filteredDoctors = this.doctors;
					break;
				case 'available':
					this.filteredDoctors = this.doctors.filter(doctor => doctor.available);
					break;
				case 'expert':
					this.filteredDoctors = this.doctors.filter(doctor => doctor.isExpert);
					break;
				case 'rating':
					this.filteredDoctors = [...this.doctors].sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating));
					break;
			}
		},
		
		// 选择医生
		selectDoctor(doctor) {
			if (!doctor.available) {
				uni.showToast({
					title: '该医生暂无号源',
					icon: 'none'
				});
				return;
			}
			this.bookAppointment(doctor);
		},
		
		// 预约挂号
		bookAppointment(doctor) {
			uni.navigateTo({
				url: `/pages/appointment/time-select?doctor=${encodeURIComponent(JSON.stringify(doctor))}`
			});
		},
		
		// 查看医生详情
		viewDoctorDetail(doctor) {
			uni.showModal({
				title: doctor.name,
				content: `职称：${doctor.title}\n科室：${doctor.department}\n擅长：${doctor.specialty}`,
				showCancel: false
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

/* 科室信息 */
.department-info {
	background: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.department-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.doctor-count {
	font-size: 26rpx;
	color: #666;
}

/* 筛选栏 */
.filter-bar {
	background: white;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	gap: 30rpx;
}

.filter-item {
	padding: 15rpx 30rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
	color: #666;
	background: #f8f8f8;
	transition: all 0.3s ease;
}

.filter-item.active {
	background: #2E8B57;
	color: white;
}

/* 医生列表 */
.doctor-list {
	padding: 0 30rpx;
}

.doctor-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.doctor-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.doctor-basic {
	display: flex;
	flex: 1;
}

.doctor-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 25rpx;
}

.doctor-info {
	flex: 1;
}

.doctor-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.doctor-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-right: 15rpx;
}

.doctor-title {
	font-size: 24rpx;
	color: #2E8B57;
	background: rgba(46, 139, 87, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.doctor-department {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.doctor-rating {
	display: flex;
	align-items: center;
}

.rating-stars {
	margin-right: 10rpx;
}

.rating-score {
	font-size: 26rpx;
	color: #FF9800;
	font-weight: bold;
	margin-right: 10rpx;
}

.rating-count {
	font-size: 24rpx;
	color: #999;
}

.doctor-status {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.status-text {
	font-size: 24rpx;
	padding: 8rpx 20rpx;
	border-radius: 15rpx;
	margin-bottom: 10rpx;
}

.status-text.available {
	background: rgba(76, 175, 80, 0.1);
	color: #4CAF50;
}

.status-text.unavailable {
	background: rgba(244, 67, 54, 0.1);
	color: #F44336;
}

.consultation-fee {
	font-size: 28rpx;
	color: #2E8B57;
	font-weight: bold;
}

.doctor-specialty {
	margin-bottom: 20rpx;
	line-height: 1.4;
}

.specialty-label {
	font-size: 26rpx;
	color: #666;
}

.specialty-text {
	font-size: 26rpx;
	color: #333;
}

.doctor-schedule {
	margin-bottom: 25rpx;
}

.schedule-label {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
	display: block;
}

.time-slots {
	display: flex;
	gap: 15rpx;
}

.time-slot {
	font-size: 24rpx;
	color: #2E8B57;
	background: rgba(46, 139, 87, 0.1);
	padding: 8rpx 20rpx;
	border-radius: 15rpx;
}

.doctor-actions {
	display: flex;
	gap: 20rpx;
}

.btn-secondary, .btn-primary {
	flex: 1;
	padding: 20rpx;
	border-radius: 15rpx;
	font-size: 28rpx;
	text-align: center;
	border: none;
}

.btn-secondary {
	background: #f0f0f0;
	color: #666;
}

.btn-primary {
	background: #2E8B57;
	color: white;
}

.btn-primary[disabled] {
	background: #ccc;
	color: #999;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 30rpx;
}

.empty-icon {
	font-size: 100rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #999;
}
</style>
