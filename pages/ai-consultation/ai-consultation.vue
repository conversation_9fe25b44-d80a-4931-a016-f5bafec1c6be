<template>
	<view class="container">
		<!-- 聊天消息区域 -->
		<scroll-view class="chat-area" scroll-y="true" :scroll-top="scrollTop" scroll-with-animation="true">
			<view class="message-list">
				<!-- AI欢迎消息 -->
				<view class="message ai-message" v-if="messages.length === 0">
					<view class="avatar">
						<image src="/static/ai-doctor.png" mode="aspectFit"></image>
					</view>
					<view class="message-content">
						<view class="message-bubble ai-bubble">
							<text>你好！我是AI智能问诊助手 🤖</text>
							<text>请告诉我你哪里不舒服，我会帮你分析症状并推荐合适的科室和医生。</text>
						</view>
					</view>
				</view>

				<!-- 消息列表 -->
				<view class="message" v-for="(message, index) in messages" :key="index"
					:class="message.type === 'user' ? 'user-message' : 'ai-message'">
					<view class="avatar" v-if="message.type === 'ai'">
						<image src="/static/ai-doctor.png" mode="aspectFit"></image>
					</view>
					<view class="message-content">
						<view class="message-bubble" :class="message.type === 'user' ? 'user-bubble' : 'ai-bubble'">
							<text>{{ message.content }}</text>
						</view>
						<view class="message-time">{{ message.time }}</view>
					</view>
					<view class="avatar" v-if="message.type === 'user'">
						<image src="/static/default-avatar.png" mode="aspectFit"></image>
					</view>
				</view>

				<!-- AI正在输入提示 -->
				<view class="message ai-message" v-if="isAITyping">
					<view class="avatar">
						<image src="/static/ai-doctor.png" mode="aspectFit"></image>
					</view>
					<view class="message-content">
						<view class="message-bubble ai-bubble typing">
							<view class="typing-indicator">
								<view class="dot"></view>
								<view class="dot"></view>
								<view class="dot"></view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 快捷回复选项 -->
		<view class="quick-replies" v-if="quickReplies.length > 0">
			<view class="quick-reply-item" v-for="(reply, index) in quickReplies" :key="index"
				@click="selectQuickReply(reply)">
				<text>{{ reply }}</text>
			</view>
		</view>

		<!-- 推荐医生卡片 -->
		<view class="doctor-recommendation" v-if="recommendedDoctor">
			<view class="recommendation-header">
				<text class="recommendation-title">为你推荐</text>
			</view>
			<view class="doctor-card">
				<view class="doctor-info">
					<image class="doctor-avatar" :src="recommendedDoctor.avatar" mode="aspectFit"></image>
					<view class="doctor-details">
						<text class="doctor-name">{{ recommendedDoctor.name }}</text>
						<text class="doctor-department">{{ recommendedDoctor.department }}</text>
						<text class="doctor-specialty">擅长：{{ recommendedDoctor.specialty }}</text>
						<view class="doctor-rating">
							<text class="rating-text">患者评价：</text>
							<text class="rating-stars">⭐⭐⭐⭐⭐</text>
							<text class="rating-score">{{ recommendedDoctor.rating }}分</text>
						</view>
					</view>
				</view>
				<view class="doctor-actions">
					<button class="btn-secondary" @click="viewOtherDoctors">看其他医生</button>
					<button class="btn-primary" @click="selectDoctor">选择{{ recommendedDoctor.name }}</button>
				</view>
			</view>
		</view>

		<!-- 输入区域 -->
		<view class="input-area">
			<view class="input-container">
				<input class="message-input" v-model="inputMessage" placeholder="描述你的症状..." @confirm="sendMessage"
					confirm-type="send" />
				<button class="send-button" @click="sendMessage" :disabled="!inputMessage.trim()">
					<text class="send-icon">➤</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			messages: [],
			inputMessage: '',
			scrollTop: 0,
			isAITyping: false,
			quickReplies: [],
			recommendedDoctor: null,
			consultationStep: 'initial', // initial, symptom_collection, analysis, recommendation
			userSymptoms: []
		}
	},
	onLoad() {
		// 初始化快捷回复选项
		this.quickReplies = [
			'我发烧了',
			'头疼',
			'肚子疼',
			'咳嗽',
			'胸闷',
			'失眠'
		];
	},
	methods: {
		// 发送消息
		sendMessage() {
			if (!this.inputMessage.trim()) return;

			const userMessage = {
				type: 'user',
				content: this.inputMessage,
				time: this.getCurrentTime()
			};

			this.messages.push(userMessage);
			this.userSymptoms.push(this.inputMessage);

			// 清空输入框
			const currentInput = this.inputMessage;
			this.inputMessage = '';

			// 滚动到底部
			this.scrollToBottom();

			// AI回复
			this.handleAIResponse(currentInput);
		},

		// 选择快捷回复
		selectQuickReply(reply) {
			this.inputMessage = reply;
			this.sendMessage();
		},

		// 处理AI回复
		handleAIResponse(userInput) {
			this.isAITyping = true;
			this.quickReplies = [];

			setTimeout(() => {
				this.isAITyping = false;

				let aiResponse = '';
				let newQuickReplies = [];

				if (this.consultationStep === 'initial') {
					// 第一次症状收集
					aiResponse = this.getSymptomAnalysisResponse(userInput);
					newQuickReplies = ['是的', '没有了', '还有其他症状'];
					this.consultationStep = 'symptom_collection';
				} else if (this.consultationStep === 'symptom_collection') {
					// 继续收集症状或进行分析
					if (userInput.includes('没有') || userInput.includes('是的')) {
						aiResponse = this.getDepartmentRecommendation();
						this.consultationStep = 'analysis';
					} else {
						aiResponse = '还有其他不舒服的地方吗？';
						newQuickReplies = ['没有了', '头疼', '恶心', '乏力'];
					}
				} else if (this.consultationStep === 'analysis') {
					// 显示医生推荐
					this.showDoctorRecommendation();
					aiResponse = '我为你推荐了一位专业的医生，你可以查看详情并预约。';
					this.consultationStep = 'recommendation';
				}

				const aiMessage = {
					type: 'ai',
					content: aiResponse,
					time: this.getCurrentTime()
				};

				this.messages.push(aiMessage);
				this.quickReplies = newQuickReplies;
				this.scrollToBottom();
			}, 1500);
		},

		// 获取症状分析回复
		getSymptomAnalysisResponse(symptom) {
			const responses = {
				'发烧': '发烧多久了？还有其他症状吗？比如头疼、咳嗽、乏力等？',
				'头疼': '头疼是什么时候开始的？疼痛程度如何？有发烧吗？',
				'肚子疼': '肚子哪个部位疼？疼了多久？有恶心呕吐吗？',
				'咳嗽': '咳嗽多久了？有痰吗？有发烧吗？',
				'胸闷': '胸闷多久了？有胸痛吗？呼吸困难吗？',
				'失眠': '失眠多久了？入睡困难还是容易醒？有什么压力吗？'
			};

			for (let key in responses) {
				if (symptom.includes(key)) {
					return responses[key];
				}
			}

			return '请详细描述一下你的症状，这样我能更好地帮助你。';
		},

		// 获取科室推荐
		getDepartmentRecommendation() {
			const symptoms = this.userSymptoms.join(' ');

			if (symptoms.includes('发烧') || symptoms.includes('头疼')) {
				return '根据你的症状，建议挂内科。内科医生专门处理发热、头疼等症状。需要帮你预约吗？';
			} else if (symptoms.includes('肚子疼') || symptoms.includes('恶心')) {
				return '根据你的症状，建议挂消化内科。消化内科医生专门处理腹痛、消化不良等问题。';
			} else if (symptoms.includes('咳嗽') || symptoms.includes('胸闷')) {
				return '根据你的症状，建议挂呼吸内科。呼吸内科医生专门处理咳嗽、胸闷等呼吸系统问题。';
			} else if (symptoms.includes('失眠')) {
				return '根据你的症状，建议挂神经内科或心理科。这些科室的医生专门处理睡眠障碍问题。';
			}

			return '根据你的症状，建议先挂内科进行初步检查，医生会根据情况进一步诊断。';
		},

		// 显示医生推荐
		showDoctorRecommendation() {
			this.recommendedDoctor = {
				name: '张主任',
				department: '内科',
				specialty: '呼吸道感染、发热诊治',
				rating: '4.8',
				avatar: '/static/doctor-zhang.png'
			};
		},

		// 选择医生
		selectDoctor() {
			uni.navigateTo({
				url: '/pages/appointment/time-select?doctor=' + encodeURIComponent(JSON.stringify(this.recommendedDoctor))
			});
		},

		// 查看其他医生
		viewOtherDoctors() {
			uni.navigateTo({
				url: '/pages/appointment/doctor-list?department=内科'
			});
		},

		// 获取当前时间
		getCurrentTime() {
			const now = new Date();
			return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
		},

		// 滚动到底部
		scrollToBottom() {
			this.$nextTick(() => {
				this.scrollTop = 999999;
			});
		}
	}
}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: #f5f5f5;
}

/* 聊天区域 */
.chat-area {
	flex: 1;
	padding: 20rpx;
}

.message-list {
	padding-bottom: 20rpx;
}

.message {
	display: flex;
	margin-bottom: 30rpx;
	align-items: flex-start;
}

.user-message {
	flex-direction: row-reverse;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	overflow: hidden;
	margin: 0 20rpx;
	flex-shrink: 0;
}

.avatar image {
	width: 100%;
	height: 100%;
}

.message-content {
	flex: 1;
	max-width: 70%;
}

.message-bubble {
	padding: 25rpx 30rpx;
	border-radius: 25rpx;
	line-height: 1.4;
	word-wrap: break-word;
}

.ai-bubble {
	background: white;
	border-bottom-left-radius: 8rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.user-bubble {
	background: #2E8B57;
	color: white;
	border-bottom-right-radius: 8rpx;
	margin-left: auto;
}

.message-time {
	font-size: 22rpx;
	color: #999;
	margin-top: 10rpx;
	text-align: center;
}

.user-message .message-time {
	text-align: right;
}

/* AI输入中动画 */
.typing {
	padding: 20rpx 30rpx !important;
}

.typing-indicator {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #2E8B57;
	animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
	animation-delay: -0.32s;
}

.dot:nth-child(2) {
	animation-delay: -0.16s;
}

@keyframes typing {

	0%,
	80%,
	100% {
		transform: scale(0.8);
		opacity: 0.5;
	}

	40% {
		transform: scale(1);
		opacity: 1;
	}
}

/* 快捷回复 */
.quick-replies {
	padding: 20rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.quick-reply-item {
	background: white;
	border: 2rpx solid #2E8B57;
	border-radius: 50rpx;
	padding: 15rpx 25rpx;
	font-size: 28rpx;
	color: #2E8B57;
	transition: all 0.3s ease;
}

.quick-reply-item:active {
	background: #2E8B57;
	color: white;
	transform: scale(0.95);
}

/* 医生推荐卡片 */
.doctor-recommendation {
	margin: 20rpx;
	background: white;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.recommendation-header {
	background: linear-gradient(135deg, #2E8B57 0%, #20B2AA 100%);
	padding: 20rpx 30rpx;
}

.recommendation-title {
	color: white;
	font-size: 32rpx;
	font-weight: bold;
}

.doctor-card {
	padding: 30rpx;
}

.doctor-info {
	display: flex;
	margin-bottom: 25rpx;
}

.doctor-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 25rpx;
}

.doctor-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.doctor-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.doctor-department {
	font-size: 28rpx;
	color: #2E8B57;
	margin-bottom: 8rpx;
}

.doctor-specialty {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 10rpx;
	line-height: 1.3;
}

.doctor-rating {
	display: flex;
	align-items: center;
}

.rating-text {
	font-size: 24rpx;
	color: #666;
	margin-right: 10rpx;
}

.rating-stars {
	margin-right: 10rpx;
}

.rating-score {
	font-size: 24rpx;
	color: #FF9800;
	font-weight: bold;
}

.doctor-actions {
	display: flex;
	gap: 20rpx;
}

.btn-secondary,
.btn-primary {
	flex: 1;
	padding: 20rpx;
	border-radius: 15rpx;
	font-size: 28rpx;
	text-align: center;
	border: none;
}

.btn-secondary {
	background: #f0f0f0;
	color: #666;
}

.btn-primary {
	background: #2E8B57;
	color: white;
}

/* 输入区域 */
.input-area {
	background: white;
	padding: 20rpx;
	border-top: 1rpx solid #eee;
}

.input-container {
	display: flex;
	align-items: center;
	background: #f8f8f8;
	border-radius: 50rpx;
	padding: 10rpx 20rpx;
}

.message-input {
	flex: 1;
	border: none;
	background: transparent;
	font-size: 30rpx;
	padding: 15rpx 0;
}

.send-button {
	width: 70rpx;
	height: 70rpx;
	border-radius: 35rpx;
	background: #2E8B57;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 15rpx;
}

.send-button[disabled] {
	background: #ccc;
}

.send-icon {
	color: white;
	font-size: 30rpx;
}
</style>
