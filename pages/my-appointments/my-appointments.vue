<template>
	<view class="container">
		<!-- 状态筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item" :class="{ active: selectedStatus === 'all' }" @click="filterAppointments('all')">
				<text>全部</text>
			</view>
			<view class="filter-item" :class="{ active: selectedStatus === 'upcoming' }"
				@click="filterAppointments('upcoming')">
				<text>待就诊</text>
			</view>
			<view class="filter-item" :class="{ active: selectedStatus === 'completed' }"
				@click="filterAppointments('completed')">
				<text>已完成</text>
			</view>
			<view class="filter-item" :class="{ active: selectedStatus === 'cancelled' }"
				@click="filterAppointments('cancelled')">
				<text>已取消</text>
			</view>
		</view>

		<!-- 预约列表 -->
		<view class="appointment-list" v-if="filteredAppointments.length > 0">
			<view class="appointment-card" v-for="appointment in filteredAppointments" :key="appointment.id">
				<!-- 预约状态标识 -->
				<view class="status-badge" :class="appointment.status">
					<text>{{ getStatusText(appointment.status) }}</text>
				</view>

				<!-- 预约信息 -->
				<view class="appointment-info">
					<view class="appointment-header">
						<view class="doctor-info">
							<image class="doctor-avatar" :src="appointment.doctor.avatar" mode="aspectFit"></image>
							<view class="doctor-details">
								<text class="doctor-name">{{ appointment.doctor.name }}</text>
								<text class="doctor-title">{{ appointment.doctor.title }}</text>
								<text class="department">{{ appointment.department }}</text>
							</view>
						</view>
						<view class="appointment-time">
							<text class="date">{{ appointment.date }}</text>
							<text class="time">{{ appointment.time }}</text>
						</view>
					</view>

					<view class="appointment-details">
						<view class="detail-item">
							<text class="label">📍 就诊地点：</text>
							<text class="value">{{ appointment.location }}</text>
						</view>
						<view class="detail-item">
							<text class="label">💰 挂号费：</text>
							<text class="value">¥{{ appointment.fee }}</text>
						</view>
						<view class="detail-item" v-if="appointment.queueNumber">
							<text class="label">🎫 排队号：</text>
							<text class="value">{{ appointment.queueNumber }}</text>
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="appointment-actions">
					<button class="btn-secondary" v-if="appointment.status === 'upcoming'"
						@click="cancelAppointment(appointment)">
						取消预约
					</button>
					<button class="btn-secondary" v-if="appointment.status === 'upcoming'"
						@click="rescheduleAppointment(appointment)">
						改约
					</button>
					<button class="btn-primary" v-if="appointment.status === 'upcoming'" @click="viewQueueStatus(appointment)">
						查看排队
					</button>
					<button class="btn-secondary" v-if="appointment.status === 'completed'" @click="viewReport(appointment)">
						查看报告
					</button>
					<button class="btn-primary" v-if="appointment.status === 'completed'" @click="rateDoctor(appointment)">
						评价医生
					</button>
				</view>

				<!-- 就诊提醒 -->
				<view class="reminder-section" v-if="appointment.status === 'upcoming' && appointment.reminder">
					<view class="reminder-header">
						<text class="reminder-icon">⏰</text>
						<text class="reminder-title">就诊提醒</text>
					</view>
					<view class="reminder-content">
						<text class="reminder-item">✅ 请携带身份证和医保卡</text>
						<text class="reminder-item">✅ 提前15分钟到达科室</text>
						<text class="reminder-item" v-if="appointment.needFasting">✅ 需要空腹检查，请勿进食</text>
						<text class="reminder-item">✅ 如有疑问可咨询导诊台</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<text class="empty-icon">📅</text>
			<text class="empty-text">{{ getEmptyText() }}</text>
			<text class="empty-desc">{{ getEmptyDesc() }}</text>
			<button class="btn-primary" @click="goToAppointment"
				v-if="selectedStatus === 'all' || selectedStatus === 'upcoming'">
				立即预约
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			selectedStatus: 'all',
			appointments: [
				{
					id: 1,
					status: 'upcoming',
					doctor: {
						name: '张主任',
						title: '主任医师',
						avatar: '/static/doctor-zhang.png'
					},
					department: '内科',
					date: '12月15日',
					time: '09:00-09:30',
					location: '门诊2楼内科',
					fee: 35,
					queueNumber: 'A08',
					reminder: true,
					needFasting: false
				},
				{
					id: 2,
					status: 'completed',
					doctor: {
						name: '李医生',
						title: '副主任医师',
						avatar: '/static/doctor-li.png'
					},
					department: '消化内科',
					date: '12月10日',
					time: '14:00-14:30',
					location: '门诊3楼消化内科',
					fee: 25,
					reminder: false
				},
				{
					id: 3,
					status: 'cancelled',
					doctor: {
						name: '王教授',
						title: '主任医师',
						avatar: '/static/doctor-wang.png'
					},
					department: '心内科',
					date: '12月8日',
					time: '10:00-10:30',
					location: '门诊4楼心内科',
					fee: 50,
					reminder: false
				}
			],
			filteredAppointments: []
		}
	},
	onLoad() {
		this.filteredAppointments = this.appointments;
	},
	onShow() {
		// 页面显示时刷新数据
		this.filterAppointments(this.selectedStatus);
	},
	methods: {
		// 筛选预约
		filterAppointments(status) {
			this.selectedStatus = status;

			if (status === 'all') {
				this.filteredAppointments = this.appointments;
			} else {
				this.filteredAppointments = this.appointments.filter(appointment => appointment.status === status);
			}
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'upcoming': '待就诊',
				'completed': '已完成',
				'cancelled': '已取消'
			};
			return statusMap[status] || '';
		},

		// 取消预约
		cancelAppointment(appointment) {
			uni.showModal({
				title: '确认取消',
				content: `确定要取消${appointment.date} ${appointment.time}的预约吗？`,
				success: (res) => {
					if (res.confirm) {
						appointment.status = 'cancelled';
						this.filterAppointments(this.selectedStatus);
						uni.showToast({
							title: '预约已取消',
							icon: 'success'
						});
					}
				}
			});
		},

		// 改约
		rescheduleAppointment(appointment) {
			uni.navigateTo({
				url: `/pages/appointment/time-select?doctor=${encodeURIComponent(JSON.stringify(appointment.doctor))}&reschedule=true`
			});
		},

		// 查看排队状态
		viewQueueStatus(appointment) {
			uni.showModal({
				title: '排队状态',
				content: `当前排队号：${appointment.queueNumber}\n预计等待时间：15-20分钟\n当前就诊：A05`,
				showCancel: false
			});
		},

		// 查看报告
		viewReport(appointment) {
			uni.showToast({
				title: '报告查看功能开发中',
				icon: 'none'
			});
		},

		// 评价医生
		rateDoctor(appointment) {
			uni.showToast({
				title: '评价功能开发中',
				icon: 'none'
			});
		},

		// 跳转到预约页面
		goToAppointment() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		},

		// 获取空状态文本
		getEmptyText() {
			const textMap = {
				'all': '暂无预约记录',
				'upcoming': '暂无待就诊预约',
				'completed': '暂无已完成预约',
				'cancelled': '暂无已取消预约'
			};
			return textMap[this.selectedStatus] || '暂无预约记录';
		},

		// 获取空状态描述
		getEmptyDesc() {
			const descMap = {
				'all': '快去预约挂号吧',
				'upcoming': '快去预约挂号吧',
				'completed': '暂无已完成的就诊记录',
				'cancelled': '暂无已取消的预约记录'
			};
			return descMap[this.selectedStatus] || '';
		}
	}
}
</script>

<style scoped>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

/* 筛选栏 */
.filter-bar {
	background: white;
	padding: 20rpx 30rpx;
	display: flex;
	gap: 30rpx;
	margin-bottom: 20rpx;
}

.filter-item {
	padding: 15rpx 30rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
	color: #666;
	background: #f8f8f8;
	transition: all 0.3s ease;
}

.filter-item.active {
	background: #2E8B57;
	color: white;
}

/* 预约列表 */
.appointment-list {
	padding: 0 30rpx;
}

.appointment-card {
	background: white;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

/* 状态标识 */
.status-badge {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	padding: 8rpx 20rpx;
	border-radius: 15rpx;
	font-size: 22rpx;
	z-index: 2;
}

.status-badge.upcoming {
	background: rgba(76, 175, 80, 0.1);
	color: #4CAF50;
}

.status-badge.completed {
	background: rgba(33, 150, 243, 0.1);
	color: #2196F3;
}

.status-badge.cancelled {
	background: rgba(244, 67, 54, 0.1);
	color: #F44336;
}

/* 预约信息 */
.appointment-info {
	padding: 30rpx;
}

.appointment-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 25rpx;
}

.doctor-info {
	display: flex;
	flex: 1;
}

.doctor-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	margin-right: 20rpx;
}

.doctor-details {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.doctor-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.doctor-title {
	font-size: 24rpx;
	color: #2E8B57;
	margin-bottom: 8rpx;
}

.department {
	font-size: 26rpx;
	color: #666;
}

.appointment-time {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	margin-right: 80rpx;
}

.date {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 5rpx;
}

.time {
	font-size: 24rpx;
	color: #666;
}

.appointment-details {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 20rpx;
}

.detail-item {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.detail-item:last-child {
	margin-bottom: 0;
}

.label {
	font-size: 26rpx;
	color: #666;
	min-width: 140rpx;
}

.value {
	font-size: 26rpx;
	color: #333;
}

/* 操作按钮 */
.appointment-actions {
	padding: 20rpx 30rpx;
	background: #f8f8f8;
	display: flex;
	gap: 15rpx;
}

.btn-secondary,
.btn-primary {
	flex: 1;
	padding: 18rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	text-align: center;
	border: none;
}

.btn-secondary {
	background: white;
	color: #666;
	border: 1rpx solid #e0e0e0;
}

.btn-primary {
	background: #2E8B57;
	color: white;
}

/* 就诊提醒 */
.reminder-section {
	background: rgba(46, 139, 87, 0.05);
	padding: 25rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

.reminder-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.reminder-icon {
	font-size: 30rpx;
	margin-right: 10rpx;
}

.reminder-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #2E8B57;
}

.reminder-content {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.reminder-item {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 150rpx 30rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 40rpx;
	opacity: 0.5;
}

.empty-text {
	font-size: 36rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.empty-desc {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 40rpx;
}

.empty-state .btn-primary {
	padding: 25rpx 50rpx;
	border-radius: 50rpx;
	font-size: 30rpx;
	background: #2E8B57;
	color: white;
	border: none;
}
</style>
