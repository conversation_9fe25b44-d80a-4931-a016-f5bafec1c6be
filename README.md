# 智好医院预约系统

基于uni-app开发的智能医院预约系统，以AI智能问诊为核心功能，提供便捷的医院预约服务。

## 🌟 核心特色

### AI智能问诊 - 核心功能
- **智能症状分析**：用户描述症状，AI自动分析并推荐合适科室
- **对话式交互**：仿微信聊天界面，用户体验友好
- **医生智能推荐**：基于症状分析推荐最适合的医生
- **完整问诊流程**：症状收集 → 智能分析 → 科室推荐 → 医生推荐 → 预约确认

### 传统预约功能 - 备选方案
- **科室选择**：常用科室快速入口 + 全部科室列表
- **医生筛选**：支持按可预约、专家、评分等条件筛选
- **时间选择**：直观的日期和时间段选择界面
- **预约确认**：详细的预约信息确认和费用说明

### 预约管理功能
- **预约记录**：支持按状态筛选（全部/待就诊/已完成/已取消）
- **预约操作**：取消预约、改约、查看排队状态
- **就诊提醒**：智能提醒携带证件、到达时间等注意事项
- **服务评价**：就诊后可对医生进行评价

## 🎨 设计理念

### 以AI为核心的产品设计
- **首页突出AI功能**：AI智能问诊占据首页核心位置，渐变色设计吸引用户
- **传统功能弱化**：传统挂号作为备选方案，给不习惯AI的用户使用
- **解决核心痛点**：专注解决年轻人"不知道挂什么科"的核心问题

### 医疗级UI设计
- **医疗绿主色调**：#2E8B57，体现专业医疗形象
- **卡片式布局**：清晰的信息层级，易于阅读
- **渐变动效**：AI功能区域添加动画效果，提升科技感

## 📱 页面结构

```
├── 首页 (pages/index/index.vue)
│   ├── 自定义导航栏（医院品牌信息）
│   ├── 用户信息卡片
│   ├── AI智能问诊（核心功能区）
│   ├── 快捷功能（挂号/预约、门诊充值）
│   └── 相关服务网格
│
├── AI智能问诊 (pages/ai-consultation/ai-consultation.vue)
│   ├── 聊天对话界面
│   ├── 快捷回复选项
│   ├── 医生推荐卡片
│   └── 消息输入区域
│
├── 传统预约流程
│   ├── 科室选择 (pages/appointment/department-list.vue)
│   ├── 医生列表 (pages/appointment/doctor-list.vue)
│   └── 时间选择 (pages/appointment/time-select.vue)
│
└── 我的预约 (pages/my-appointments/my-appointments.vue)
    ├── 状态筛选栏
    ├── 预约记录列表
    ├── 预约操作按钮
    └── 就诊提醒信息
```

## 🚀 技术栈

- **框架**：uni-app (Vue.js)
- **样式**：SCSS + 自定义主题变量
- **图标**：Emoji + 自定义图标
- **动画**：CSS3 动画效果
- **适配**：响应式设计，支持多端运行

## 💡 核心功能流程

### AI智能问诊流程示例

**用户场景：发烧症状**

1. **症状输入**：用户点击"AI智能问诊"，输入"我发烧了"
2. **AI分析**：AI回复"发烧多久了？还有其他症状吗？"
3. **症状收集**：用户回答"昨天开始的，有点头疼"
4. **科室推荐**：AI分析后推荐"建议挂内科，专门处理发热、头疼等症状"
5. **医生推荐**：推荐张主任，显示医生信息、评分、擅长领域
6. **时间选择**：选择预约时间（明天上午9:00）
7. **预约确认**：显示完整预约信息和就诊提醒
8. **预约成功**：跳转到我的预约页面

### 传统预约流程

1. **科室选择**：从常用科室或全部科室中选择
2. **医生筛选**：查看科室医生列表，支持筛选
3. **时间选择**：选择可预约的日期和时间段
4. **预约确认**：确认预约信息并完成预约

## 🎯 产品优势

1. **降低就医门槛**：AI问诊帮助用户快速找到合适科室
2. **提升用户体验**：对话式交互，操作简单直观
3. **智能化推荐**：基于症状智能推荐医生和时间
4. **完整服务闭环**：从问诊到预约到就诊提醒的完整流程

## 📋 待完善功能

- [ ] 真实AI问诊接口集成
- [ ] 用户登录和身份验证
- [ ] 支付功能集成
- [ ] 推送通知功能
- [ ] 医生详情页面
- [ ] 检查报告查看
- [ ] 在线咨询功能

## 🔧 开发说明

### 环境要求
- HBuilderX 3.0+
- uni-app 框架
- 支持微信小程序、H5、App等多端运行

### 项目结构
```
├── pages/                 # 页面文件
├── static/                # 静态资源
├── uni.scss              # 全局样式变量
├── pages.json            # 页面配置
└── manifest.json         # 应用配置
```

### 样式规范
- 使用rpx单位适配不同屏幕
- 统一使用医疗绿色主题 (#2E8B57)
- 卡片式设计，圆角20rpx
- 阴影效果增强层次感

---

**智好医院预约系统** - 让就医更智能，让预约更简单 🏥✨
